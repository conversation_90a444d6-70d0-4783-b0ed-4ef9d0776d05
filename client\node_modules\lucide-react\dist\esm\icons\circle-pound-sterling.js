/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 16V9.5a1 1 0 0 1 5 0", key: "1i1are" }],
  ["path", { d: "M8 12h4", key: "qz6y1c" }],
  ["path", { d: "M8 16h7", key: "sbedsn" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const CirclePoundSterling = createLucideIcon("circle-pound-sterling", __iconNode);

export { __iconNode, CirclePoundSterling as default };
//# sourceMappingURL=circle-pound-sterling.js.map
