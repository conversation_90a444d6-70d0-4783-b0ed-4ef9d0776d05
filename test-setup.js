const axios = require('axios');

async function testSetup() {
  console.log('🧪 Testing ResumeMate Setup...\n');

  // Test Frontend
  try {
    console.log('📱 Testing Frontend (http://localhost:5173)...');
    const frontendResponse = await axios.get('http://localhost:5173', { timeout: 5000 });
    console.log('✅ Frontend is running successfully!');
  } catch (error) {
    console.log('❌ Frontend is not accessible:', error.message);
  }

  // Test Backend Health
  try {
    console.log('\n🔧 Testing Backend Health (http://localhost:5000/api/health)...');
    const healthResponse = await axios.get('http://localhost:5000/api/health', { timeout: 5000 });
    console.log('✅ Backend is running successfully!');
    console.log('📊 Health Status:', healthResponse.data);
  } catch (error) {
    console.log('❌ Backend is not accessible:', error.message);
    console.log('💡 Make sure MongoDB is running and server is started');
  }

  // Test Database Connection (indirect)
  try {
    console.log('\n💾 Testing Database Connection...');
    const dbResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'testpassword'
    }, { timeout: 5000 });
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Database connection is working (got expected auth error)');
    } else {
      console.log('❌ Database connection issue:', error.message);
    }
  }

  console.log('\n🎉 Setup test completed!');
  console.log('\n📝 Next Steps:');
  console.log('1. Visit http://localhost:5173 to see the frontend');
  console.log('2. Register a new account to test the full flow');
  console.log('3. Configure environment variables for full functionality');
  console.log('4. Set up MongoDB if not already running');
}

testSetup().catch(console.error);
