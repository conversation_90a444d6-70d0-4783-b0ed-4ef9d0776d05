# ResumeMate - AI-Powered Resume Builder

A fullstack web application that helps users create professional resumes with AI assistance, real-time preview, and PDF export functionality.

## 🚀 Features

### ✅ Completed Features

#### 🔐 Authentication
- [x] Manual sign up/login with email & password (JWT)
- [x] Google OAuth login/signup (OAuth2 with Passport.js)
- [x] Secure password hashing with bcrypt
- [x] Protected routes and middleware

#### 📝 Resume Management
- [x] User dashboard with resume overview
- [x] Create, edit, and delete resumes
- [x] Multiple resume templates (minimalist, modern, creative)
- [x] Resume sharing with public links
- [x] Resume duplication functionality

#### 🤖 AI Integration (Backend Ready)
- [x] OpenRouter API integration for AI features
- [x] Generate professional summaries
- [x] Suggest relevant skills
- [x] Improve experience descriptions
- [x] Analyze job descriptions

#### 📬 Contact System
- [x] Contact form with Nodemailer integration
- [x] Admin email notifications
- [x] Contact message management

#### 🧑‍💻 Admin Dashboard (Backend Ready)
- [x] User management system
- [x] Contact message handling
- [x] System statistics and monitoring
- [x] Admin-only routes and permissions

### 🚧 In Development
- [ ] Resume Builder UI with real-time preview
- [ ] PDF export functionality
- [ ] Resume templates implementation
- [ ] AI features frontend integration
- [ ] Admin dashboard frontend

## 🛠️ Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose
- **JWT** for authentication
- **Passport.js** for Google OAuth
- **bcryptjs** for password hashing
- **Nodemailer** for email functionality
- **OpenRouter API** for AI features

### Frontend
- **React** with Vite
- **React Router** for navigation
- **Tailwind CSS** for styling
- **Axios** for API calls
- **React Hot Toast** for notifications
- **Lucide React** for icons

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- npm or yarn

### 1. Clone the repository
```bash
git clone <repository-url>
cd ResumeMate
```

### 2. Install dependencies
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 3. Environment Setup

#### Server Environment (.env)
Create `server/.env` file:
```env
# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:5173

# Database
MONGODB_URI=mongodb://localhost:27017/resumemate

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OpenRouter API (for AI features)
OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
ADMIN_EMAIL=<EMAIL>

# Session Secret
SESSION_SECRET=your_session_secret
```

#### Client Environment (.env)
Create `client/.env` file:
```env
VITE_API_URL=http://localhost:5000
```

### 4. Database Setup
Make sure MongoDB is running on your system or update the `MONGODB_URI` to point to your MongoDB instance.

### 5. Start the application

#### Development Mode (Both servers)
```bash
# From root directory
npm run dev
```

#### Individual servers
```bash
# Start backend server
npm run server

# Start frontend client
npm run client
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `GET /api/auth/google` - Google OAuth login
- `POST /api/auth/logout` - Logout user

### Resume Management
- `GET /api/resume` - Get user's resumes
- `POST /api/resume` - Create new resume
- `GET /api/resume/:id` - Get specific resume
- `PUT /api/resume/:id` - Update resume
- `DELETE /api/resume/:id` - Delete resume
- `POST /api/resume/:id/duplicate` - Duplicate resume
- `POST /api/resume/:id/share` - Generate shareable link

### AI Features
- `POST /api/ai/generate-summary` - Generate professional summary
- `POST /api/ai/suggest-skills` - Suggest relevant skills
- `POST /api/ai/improve-experience` - Improve experience descriptions
- `POST /api/ai/analyze-job-description` - Analyze job posting

### Contact
- `POST /api/contact` - Submit contact form
- `GET /api/contact` - Get all messages (admin)
- `POST /api/contact/:id/reply` - Reply to message (admin)

### Admin
- `GET /api/admin/dashboard` - Admin dashboard stats
- `GET /api/admin/users` - Get all users
- `GET /api/admin/resumes` - Get all resumes
- `DELETE /api/admin/users/:id` - Delete user

## 🎨 Frontend Structure

```
client/src/
├── components/
│   ├── Auth/          # Authentication components
│   ├── Layout/        # Navigation, Footer
│   ├── Resume/        # Resume builder components
│   ├── Templates/     # Resume templates
│   ├── Contact/       # Contact form components
│   ├── Admin/         # Admin dashboard components
│   └── UI/            # Reusable UI components
├── context/           # React context providers
├── hooks/             # Custom React hooks
├── pages/             # Page components
├── services/          # API service functions
└── utils/             # Utility functions
```

## 🔐 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting on API endpoints
- CORS configuration
- Input validation and sanitization
- Protected routes and admin-only access

## 📧 Email Configuration

The application uses Nodemailer for sending emails. Configure your email provider:

1. **Gmail**: Use App Passwords for authentication
2. **Other providers**: Update SMTP settings in environment variables

## 🤖 AI Integration

The application integrates with OpenRouter API for AI features:

1. Get an API key from [OpenRouter](https://openrouter.ai/)
2. Add the key to your environment variables
3. AI features will be available in the resume builder

## 🚀 Deployment

### Backend Deployment
1. Set up MongoDB Atlas or your preferred database
2. Configure environment variables for production
3. Deploy to platforms like Heroku, Railway, or DigitalOcean

### Frontend Deployment
1. Build the client: `cd client && npm run build`
2. Deploy to Vercel, Netlify, or serve statically

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

## 🎯 Roadmap

- [ ] Complete resume builder UI
- [ ] Implement PDF export
- [ ] Add more resume templates
- [ ] Mobile app development
- [ ] Advanced AI features
- [ ] Resume analytics
- [ ] Job application tracking

---

Built with ❤️ for job seekers everywhere!
