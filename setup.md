# ResumeMate Setup Guide

## Quick Setup Instructions

### 1. Copy Environment Files
```bash
# Copy server environment file
cp server/.env.example server/.env

# Copy client environment file  
cp client/.env.example client/.env
```

### 2. Update Environment Variables

Edit `server/.env` with your actual values:
- Add a strong JWT secret
- Configure MongoDB URI
- Add Google OAuth credentials (optional)
- Add OpenRouter API key for AI features (optional)
- Configure email settings for contact form

### 3. Install Dependencies
```bash
npm run install-all
```

### 4. Start Development Servers
```bash
npm run dev
```

This will start both the backend (port 5000) and frontend (port 5173) servers.

### 5. Access the Application
- Frontend: http://localhost:5173
- Backend API: http://localhost:5000

## Optional Configuration

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:5000/api/auth/google/callback`
6. Update `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` in server/.env

### OpenRouter AI Setup
1. Sign up at [OpenRouter](https://openrouter.ai/)
2. Get your API key
3. Update `OPENROUTER_API_KEY` in server/.env

### Email Configuration
1. For Gmail: Enable 2FA and create an App Password
2. Update email settings in server/.env
3. Test contact form functionality

## Database Setup

### Local MongoDB
```bash
# Install MongoDB locally or use Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

### MongoDB Atlas (Cloud)
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a cluster
3. Get connection string
4. Update `MONGODB_URI` in server/.env

## Testing the Application

1. **Register a new user** at http://localhost:5173/register
2. **Login** and access the dashboard
3. **Test contact form** functionality
4. **Create admin user** by manually updating the database or registering and setting `isAdmin: true`

## Troubleshooting

### Common Issues
- **Port conflicts**: Change ports in environment files
- **MongoDB connection**: Ensure MongoDB is running
- **CORS errors**: Check CLIENT_URL in server/.env
- **Build errors**: Clear node_modules and reinstall

### Logs
- Backend logs: Check terminal running server
- Frontend logs: Check browser console
- Database logs: Check MongoDB logs

## Production Deployment

### Environment Variables for Production
- Set `NODE_ENV=production`
- Use strong, unique secrets
- Configure production database
- Set up proper CORS origins
- Enable HTTPS

### Security Checklist
- [ ] Strong JWT secret
- [ ] Secure database credentials
- [ ] HTTPS enabled
- [ ] Rate limiting configured
- [ ] Input validation enabled
- [ ] CORS properly configured
