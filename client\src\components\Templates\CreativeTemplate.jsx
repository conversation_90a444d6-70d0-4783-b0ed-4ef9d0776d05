import React from 'react';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

const CreativeTemplate = ({ data }) => {
  const {
    personalInfo = {},
    summary = '',
    experience = [],
    education = [],
    skills = { technical: [], soft: [], languages: [] },
    projects = [],
    certifications = []
  } = data;

  return (
    <div className="bg-white text-gray-900 leading-relaxed print-avoid-break">
      {/* Header */}
      <header className="relative bg-gradient-to-br from-purple-600 via-pink-600 to-orange-500 text-white p-8 mb-0 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="relative z-10">
          <h1 className="text-5xl font-bold mb-4 text-shadow">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          
          <div className="flex flex-wrap gap-6 text-sm">
            {personalInfo.email && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <Mail className="h-4 w-4" />
                <span>{personalInfo.email}</span>
              </div>
            )}
            {personalInfo.phone && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <Phone className="h-4 w-4" />
                <span>{personalInfo.phone}</span>
              </div>
            )}
            {personalInfo.location && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <MapPin className="h-4 w-4" />
                <span>{personalInfo.location}</span>
              </div>
            )}
            {personalInfo.website && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <Globe className="h-4 w-4" />
                <span>Portfolio</span>
              </div>
            )}
            {personalInfo.linkedin && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <Linkedin className="h-4 w-4" />
                <span>LinkedIn</span>
              </div>
            )}
            {personalInfo.github && (
              <div className="flex items-center gap-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
                <Github className="h-4 w-4" />
                <span>GitHub</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -ml-12 -mb-12"></div>
      </header>

      <div className="p-8">
        {/* Professional Summary */}
        {summary && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                About Me
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <p className="text-gray-700 leading-relaxed text-lg mt-6 pl-20">{summary}</p>
          </section>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Experience
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <div className="space-y-8 mt-6">
              {experience.map((exp, index) => (
                <div key={index} className="relative print-avoid-break">
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl border-l-4 border-gradient-to-b from-purple-500 to-pink-500">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{exp.position}</h3>
                        <p className="text-purple-700 font-semibold text-lg">{exp.company}</p>
                      </div>
                      <div className="text-right bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-full">
                        <p className="font-medium text-sm">{exp.startDate} - {exp.endDate}</p>
                        {exp.location && <p className="text-xs opacity-90">{exp.location}</p>}
                      </div>
                    </div>
                    {exp.description && exp.description.length > 0 && (
                      <ul className="space-y-2 text-gray-700">
                        {exp.description.map((desc, descIndex) => (
                          desc.trim() && (
                            <li key={descIndex} className="flex items-start">
                              <span className="text-purple-500 mr-3 mt-1 text-lg">●</span>
                              <span className="leading-relaxed">{desc}</span>
                            </li>
                          )
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {(skills.technical.length > 0 || skills.soft.length > 0 || skills.languages.length > 0) && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Skills
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <div className="mt-6 space-y-6">
              {skills.technical.length > 0 && (
                <div>
                  <h3 className="font-bold text-purple-800 mb-3 text-lg">Technical Expertise</h3>
                  <div className="flex flex-wrap gap-3">
                    {skills.technical.map((skill, index) => (
                      <span key={index} className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {skills.soft.length > 0 && (
                <div>
                  <h3 className="font-bold text-pink-800 mb-3 text-lg">Soft Skills</h3>
                  <div className="flex flex-wrap gap-3">
                    {skills.soft.map((skill, index) => (
                      <span key={index} className="bg-gradient-to-r from-pink-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {skills.languages.length > 0 && (
                <div>
                  <h3 className="font-bold text-orange-800 mb-3 text-lg">Languages</h3>
                  <div className="flex flex-wrap gap-3">
                    {skills.languages.map((language, index) => (
                      <span key={index} className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                        {language}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </section>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Featured Projects
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {projects.map((project, index) => (
                <div key={index} className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-200 print-avoid-break">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{project.name}</h3>
                  {project.description && (
                    <p className="text-gray-700 leading-relaxed mb-4">{project.description}</p>
                  )}
                  <div className="space-y-3">
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-white text-purple-700 px-3 py-1 rounded-full text-xs font-medium border border-purple-200">
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                    <div className="flex gap-4 text-sm">
                      {project.url && (
                        <span className="text-purple-600 font-medium">🔗 Live Demo</span>
                      )}
                      {project.github && (
                        <span className="text-purple-600 font-medium">📁 Source Code</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {education.length > 0 && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Education
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <div className="space-y-4 mt-6">
              {education.map((edu, index) => (
                <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl print-avoid-break">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{edu.degree}</h3>
                      <p className="text-purple-700 font-semibold">{edu.institution}</p>
                      {edu.field && <p className="text-gray-600">{edu.field}</p>}
                    </div>
                    <div className="text-right bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-full">
                      <p className="font-medium text-sm">{edu.startDate} - {edu.endDate}</p>
                      {edu.gpa && <p className="text-xs opacity-90">GPA: {edu.gpa}</p>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Certifications */}
        {certifications.length > 0 && (
          <section className="mb-8">
            <div className="relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                Certifications
              </h2>
              <div className="absolute left-0 top-8 w-16 h-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              {certifications.map((cert, index) => (
                <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200 print-avoid-break">
                  <h3 className="font-bold text-gray-900">{cert.name}</h3>
                  <p className="text-purple-700">{cert.issuer}</p>
                  <p className="text-sm text-gray-600">{cert.date}</p>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default CreativeTemplate;
