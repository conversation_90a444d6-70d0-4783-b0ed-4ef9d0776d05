import React from 'react';
import { Check } from 'lucide-react';

const TemplateSelector = ({ selectedTemplate, onTemplateChange }) => {
  const templates = [
    {
      id: 'minimalist',
      name: 'Minimalist',
      description: 'Clean and simple design perfect for any industry',
      preview: '/templates/minimalist-preview.png',
      features: ['ATS-Friendly', 'Clean Layout', 'Professional']
    },
    {
      id: 'modern',
      name: 'Modern',
      description: 'Contemporary design with subtle colors and modern typography',
      preview: '/templates/modern-preview.png',
      features: ['Eye-catching', 'Color Accents', 'Modern Typography']
    },
    {
      id: 'creative',
      name: 'Creative',
      description: 'Bold design for creative professionals and designers',
      preview: '/templates/creative-preview.png',
      features: ['Creative Layout', 'Visual Elements', 'Unique Design']
    }
  ];

  return (
    <div className="mb-8">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Choose Template</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {templates.map((template) => (
          <div
            key={template.id}
            className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedTemplate === template.id
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => onTemplateChange(template.id)}
          >
            {selectedTemplate === template.id && (
              <div className="absolute top-2 right-2 bg-primary-500 text-white rounded-full p-1">
                <Check className="h-3 w-3" />
              </div>
            )}
            
            {/* Template Preview */}
            <div className="aspect-[3/4] bg-gray-100 rounded-md mb-3 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-20 bg-white border border-gray-300 rounded shadow-sm mx-auto mb-2 flex flex-col">
                  {template.id === 'minimalist' && (
                    <div className="p-1 space-y-1">
                      <div className="h-1 bg-gray-800 rounded"></div>
                      <div className="h-0.5 bg-gray-600 rounded w-3/4"></div>
                      <div className="h-0.5 bg-gray-400 rounded w-1/2"></div>
                      <div className="space-y-0.5 mt-1">
                        <div className="h-0.5 bg-gray-300 rounded"></div>
                        <div className="h-0.5 bg-gray-300 rounded w-4/5"></div>
                        <div className="h-0.5 bg-gray-300 rounded w-3/5"></div>
                      </div>
                    </div>
                  )}
                  {template.id === 'modern' && (
                    <div className="p-1 space-y-1">
                      <div className="h-1 bg-blue-600 rounded"></div>
                      <div className="h-0.5 bg-gray-800 rounded w-3/4"></div>
                      <div className="h-0.5 bg-blue-500 rounded w-1/2"></div>
                      <div className="space-y-0.5 mt-1">
                        <div className="h-0.5 bg-gray-400 rounded"></div>
                        <div className="h-0.5 bg-gray-400 rounded w-4/5"></div>
                        <div className="h-0.5 bg-blue-400 rounded w-3/5"></div>
                      </div>
                    </div>
                  )}
                  {template.id === 'creative' && (
                    <div className="p-1 space-y-1">
                      <div className="h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded"></div>
                      <div className="h-0.5 bg-gray-800 rounded w-3/4"></div>
                      <div className="h-0.5 bg-purple-500 rounded w-1/2"></div>
                      <div className="space-y-0.5 mt-1">
                        <div className="h-0.5 bg-pink-400 rounded"></div>
                        <div className="h-0.5 bg-gray-400 rounded w-4/5"></div>
                        <div className="h-0.5 bg-purple-400 rounded w-3/5"></div>
                      </div>
                    </div>
                  )}
                </div>
                <span className="text-xs text-gray-600">{template.name}</span>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-1">{template.name}</h4>
              <p className="text-sm text-gray-600 mb-3">{template.description}</p>
              
              <div className="flex flex-wrap gap-1">
                {template.features.map((feature, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TemplateSelector;
