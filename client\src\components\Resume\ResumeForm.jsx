import React, { useState } from "react";
import {
  Plus,
  Trash2,
  User,
  Briefcase,
  GraduationCap,
  Code,
  Award,
} from "lucide-react";

const ResumeForm = ({ resumeData, onChange }) => {
  const [activeSection, setActiveSection] = useState("personal");

  const updateField = (section, field, value) => {
    if (section === "personalInfo") {
      onChange((prev) => ({
        ...prev,
        personalInfo: {
          ...prev.personalInfo,
          [field]: value,
        },
      }));
    } else {
      onChange((prev) => ({
        ...prev,
        [section]: value,
      }));
    }
  };

  const addArrayItem = (section, item) => {
    onChange((prev) => ({
      ...prev,
      [section]: [...prev[section], item],
    }));
  };

  const updateArrayItem = (section, index, item) => {
    onChange((prev) => ({
      ...prev,
      [section]: prev[section].map((existing, i) =>
        i === index ? item : existing
      ),
    }));
  };

  const removeArrayItem = (section, index) => {
    onChange((prev) => ({
      ...prev,
      [section]: prev[section].filter((_, i) => i !== index),
    }));
  };

  const sections = [
    { id: "personal", label: "Personal Info", icon: User },
    { id: "summary", label: "Summary", icon: User },
    { id: "experience", label: "Experience", icon: Briefcase },
    { id: "education", label: "Education", icon: GraduationCap },
    { id: "skills", label: "Skills", icon: Code },
    { id: "projects", label: "Projects", icon: Code },
    { id: "certifications", label: "Certifications", icon: Award },
  ];

  return (
    <div className="space-y-6">
      {/* Section Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 overflow-x-auto">
          {sections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeSection === section.id
                    ? "border-primary-500 text-primary-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{section.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Personal Information */}
      {activeSection === "personal" && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Personal Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                value={resumeData.personalInfo.fullName}
                onChange={(e) =>
                  updateField("personalInfo", "fullName", e.target.value)
                }
                className="input"
                placeholder="John Doe"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                value={resumeData.personalInfo.email}
                onChange={(e) =>
                  updateField("personalInfo", "email", e.target.value)
                }
                className="input"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={resumeData.personalInfo.phone}
                onChange={(e) =>
                  updateField("personalInfo", "phone", e.target.value)
                }
                className="input"
                placeholder="+****************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                type="text"
                value={resumeData.personalInfo.location}
                onChange={(e) =>
                  updateField("personalInfo", "location", e.target.value)
                }
                className="input"
                placeholder="New York, NY"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                value={resumeData.personalInfo.website}
                onChange={(e) =>
                  updateField("personalInfo", "website", e.target.value)
                }
                className="input"
                placeholder="https://johndoe.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LinkedIn
              </label>
              <input
                type="url"
                value={resumeData.personalInfo.linkedin}
                onChange={(e) =>
                  updateField("personalInfo", "linkedin", e.target.value)
                }
                className="input"
                placeholder="https://linkedin.com/in/johndoe"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                GitHub
              </label>
              <input
                type="url"
                value={resumeData.personalInfo.github}
                onChange={(e) =>
                  updateField("personalInfo", "github", e.target.value)
                }
                className="input"
                placeholder="https://github.com/johndoe"
              />
            </div>
          </div>
        </div>
      )}

      {/* Summary */}
      {activeSection === "summary" && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Professional Summary
          </h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Summary
            </label>
            <textarea
              value={resumeData.summary}
              onChange={(e) => updateField("summary", null, e.target.value)}
              className="textarea"
              rows={4}
              placeholder="Write a compelling professional summary that highlights your key achievements and skills..."
            />
          </div>
        </div>
      )}

      {/* Experience */}
      {activeSection === "experience" && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Work Experience
            </h3>
            <button
              onClick={() =>
                addArrayItem("experience", {
                  company: "",
                  position: "",
                  location: "",
                  startDate: "",
                  endDate: "",
                  current: false,
                  description: [""],
                })
              }
              className="btn-primary btn-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Experience
            </button>
          </div>

          {resumeData.experience.map((exp, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 space-y-4"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-gray-900">
                  Experience {index + 1}
                </h4>
                <button
                  onClick={() => removeArrayItem("experience", index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company *
                  </label>
                  <input
                    type="text"
                    value={exp.company}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        company: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Company Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Position *
                  </label>
                  <input
                    type="text"
                    value={exp.position}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        position: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Job Title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    value={exp.location}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        location: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="City, State"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date *
                  </label>
                  <input
                    type="text"
                    value={exp.startDate}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        startDate: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="MM/YYYY"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="text"
                    value={exp.endDate}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        endDate: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="MM/YYYY or Present"
                    disabled={exp.current}
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={exp.current}
                    onChange={(e) =>
                      updateArrayItem("experience", index, {
                        ...exp,
                        current: e.target.checked,
                        endDate: e.target.checked ? "Present" : "",
                      })
                    }
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Currently working here
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Job Description
                </label>
                {exp.description.map((desc, descIndex) => (
                  <div
                    key={descIndex}
                    className="flex items-center space-x-2 mb-2"
                  >
                    <input
                      type="text"
                      value={desc}
                      onChange={(e) => {
                        const newDescription = [...exp.description];
                        newDescription[descIndex] = e.target.value;
                        updateArrayItem("experience", index, {
                          ...exp,
                          description: newDescription,
                        });
                      }}
                      className="input flex-1"
                      placeholder="• Describe your achievements and responsibilities..."
                    />
                    <button
                      onClick={() => {
                        const newDescription = exp.description.filter(
                          (_, i) => i !== descIndex
                        );
                        updateArrayItem("experience", index, {
                          ...exp,
                          description: newDescription,
                        });
                      }}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}
                <button
                  onClick={() => {
                    const newDescription = [...exp.description, ""];
                    updateArrayItem("experience", index, {
                      ...exp,
                      description: newDescription,
                    });
                  }}
                  className="btn-outline btn-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Bullet Point
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Education */}
      {activeSection === "education" && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Education</h3>
            <button
              onClick={() =>
                addArrayItem("education", {
                  institution: "",
                  degree: "",
                  field: "",
                  location: "",
                  startDate: "",
                  endDate: "",
                  gpa: "",
                  honors: [],
                })
              }
              className="btn-primary btn-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Education
            </button>
          </div>

          {resumeData.education.map((edu, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 space-y-4"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-gray-900">
                  Education {index + 1}
                </h4>
                <button
                  onClick={() => removeArrayItem("education", index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Institution *
                  </label>
                  <input
                    type="text"
                    value={edu.institution}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        institution: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="University Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Degree *
                  </label>
                  <input
                    type="text"
                    value={edu.degree}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        degree: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Bachelor of Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Field of Study
                  </label>
                  <input
                    type="text"
                    value={edu.field}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        field: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Computer Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    value={edu.location}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        location: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="City, State"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="text"
                    value={edu.startDate}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        startDate: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="MM/YYYY"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="text"
                    value={edu.endDate}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        endDate: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="MM/YYYY"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    GPA (Optional)
                  </label>
                  <input
                    type="text"
                    value={edu.gpa}
                    onChange={(e) =>
                      updateArrayItem("education", index, {
                        ...edu,
                        gpa: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="3.8/4.0"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Skills */}
      {activeSection === "skills" && (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900">Skills</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Technical Skills
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {resumeData.skills.technical.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                  >
                    {skill}
                    <button
                      onClick={() => {
                        const newSkills = resumeData.skills.technical.filter(
                          (_, i) => i !== index
                        );
                        onChange((prev) => ({
                          ...prev,
                          skills: { ...prev.skills, technical: newSkills },
                        }));
                      }}
                      className="ml-2 text-primary-600 hover:text-primary-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                className="input"
                placeholder="Add technical skill (press Enter)"
                onKeyPress={(e) => {
                  if (e.key === "Enter" && e.target.value.trim()) {
                    onChange((prev) => ({
                      ...prev,
                      skills: {
                        ...prev.skills,
                        technical: [
                          ...prev.skills.technical,
                          e.target.value.trim(),
                        ],
                      },
                    }));
                    e.target.value = "";
                  }
                }}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Soft Skills
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {resumeData.skills.soft.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
                  >
                    {skill}
                    <button
                      onClick={() => {
                        const newSkills = resumeData.skills.soft.filter(
                          (_, i) => i !== index
                        );
                        onChange((prev) => ({
                          ...prev,
                          skills: { ...prev.skills, soft: newSkills },
                        }));
                      }}
                      className="ml-2 text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                className="input"
                placeholder="Add soft skill (press Enter)"
                onKeyPress={(e) => {
                  if (e.key === "Enter" && e.target.value.trim()) {
                    onChange((prev) => ({
                      ...prev,
                      skills: {
                        ...prev.skills,
                        soft: [...prev.skills.soft, e.target.value.trim()],
                      },
                    }));
                    e.target.value = "";
                  }
                }}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Languages
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {resumeData.skills.languages.map((language, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {language}
                    <button
                      onClick={() => {
                        const newLanguages = resumeData.skills.languages.filter(
                          (_, i) => i !== index
                        );
                        onChange((prev) => ({
                          ...prev,
                          skills: { ...prev.skills, languages: newLanguages },
                        }));
                      }}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                className="input"
                placeholder="Add language (press Enter)"
                onKeyPress={(e) => {
                  if (e.key === "Enter" && e.target.value.trim()) {
                    onChange((prev) => ({
                      ...prev,
                      skills: {
                        ...prev.skills,
                        languages: [
                          ...prev.skills.languages,
                          e.target.value.trim(),
                        ],
                      },
                    }));
                    e.target.value = "";
                  }
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Projects */}
      {activeSection === "projects" && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Projects</h3>
            <button
              onClick={() =>
                addArrayItem("projects", {
                  name: "",
                  description: "",
                  technologies: [],
                  url: "",
                  github: "",
                  startDate: "",
                  endDate: "",
                })
              }
              className="btn-primary btn-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Project
            </button>
          </div>

          {resumeData.projects.map((project, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 space-y-4"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-gray-900">
                  Project {index + 1}
                </h4>
                <button
                  onClick={() => removeArrayItem("projects", index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Project Name *
                  </label>
                  <input
                    type="text"
                    value={project.name}
                    onChange={(e) =>
                      updateArrayItem("projects", index, {
                        ...project,
                        name: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Project Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Live URL
                  </label>
                  <input
                    type="url"
                    value={project.url}
                    onChange={(e) =>
                      updateArrayItem("projects", index, {
                        ...project,
                        url: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="https://project.com"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    GitHub Repository
                  </label>
                  <input
                    type="url"
                    value={project.github}
                    onChange={(e) =>
                      updateArrayItem("projects", index, {
                        ...project,
                        github: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="https://github.com/username/project"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    value={project.description}
                    onChange={(e) =>
                      updateArrayItem("projects", index, {
                        ...project,
                        description: e.target.value,
                      })
                    }
                    className="textarea"
                    rows={3}
                    placeholder="Describe what the project does and your role in it..."
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {activeSection === "certifications" && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Certifications
            </h3>
            <button
              onClick={() =>
                addArrayItem("certifications", {
                  name: "",
                  issuer: "",
                  date: "",
                  url: "",
                })
              }
              className="btn-primary btn-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Certification
            </button>
          </div>

          {resumeData.certifications.map((cert, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 space-y-4"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium text-gray-900">
                  Certification {index + 1}
                </h4>
                <button
                  onClick={() => removeArrayItem("certifications", index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Certification Name *
                  </label>
                  <input
                    type="text"
                    value={cert.name}
                    onChange={(e) =>
                      updateArrayItem("certifications", index, {
                        ...cert,
                        name: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="AWS Certified Solutions Architect"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Issuing Organization *
                  </label>
                  <input
                    type="text"
                    value={cert.issuer}
                    onChange={(e) =>
                      updateArrayItem("certifications", index, {
                        ...cert,
                        issuer: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="Amazon Web Services"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Obtained *
                  </label>
                  <input
                    type="text"
                    value={cert.date}
                    onChange={(e) =>
                      updateArrayItem("certifications", index, {
                        ...cert,
                        date: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="MM/YYYY"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Credential URL
                  </label>
                  <input
                    type="url"
                    value={cert.url}
                    onChange={(e) =>
                      updateArrayItem("certifications", index, {
                        ...cert,
                        url: e.target.value,
                      })
                    }
                    className="input"
                    placeholder="https://credential-url.com"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ResumeForm;
