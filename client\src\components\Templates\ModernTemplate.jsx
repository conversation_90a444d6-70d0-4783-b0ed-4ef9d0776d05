import React from 'react';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

const ModernTemplate = ({ data }) => {
  const {
    personalInfo = {},
    summary = '',
    experience = [],
    education = [],
    skills = { technical: [], soft: [], languages: [] },
    projects = [],
    certifications = []
  } = data;

  return (
    <div className="bg-white text-gray-900 leading-relaxed print-avoid-break">
      {/* Header */}
      <header className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-8 mb-0">
        <h1 className="text-4xl font-bold mb-3">
          {personalInfo.fullName || 'Your Name'}
        </h1>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            {personalInfo.email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>{personalInfo.email}</span>
              </div>
            )}
            {personalInfo.phone && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>{personalInfo.phone}</span>
              </div>
            )}
            {personalInfo.location && (
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span>{personalInfo.location}</span>
              </div>
            )}
          </div>
          <div className="space-y-2">
            {personalInfo.website && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <span>{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedin && (
              <div className="flex items-center gap-2">
                <Linkedin className="h-4 w-4" />
                <span>LinkedIn Profile</span>
              </div>
            )}
            {personalInfo.github && (
              <div className="flex items-center gap-2">
                <Github className="h-4 w-4" />
                <span>GitHub Profile</span>
              </div>
            )}
          </div>
        </div>
      </header>

      <div className="p-8">
        {/* Professional Summary */}
        {summary && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-4 border-b-2 border-blue-200 pb-2">
              Professional Summary
            </h2>
            <p className="text-gray-700 leading-relaxed text-lg">{summary}</p>
          </section>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
              Professional Experience
            </h2>
            <div className="space-y-6">
              {experience.map((exp, index) => (
                <div key={index} className="relative pl-6 print-avoid-break">
                  <div className="absolute left-0 top-2 w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="absolute left-1.5 top-5 w-0.5 h-full bg-blue-200"></div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{exp.position}</h3>
                        <p className="text-blue-700 font-semibold text-lg">{exp.company}</p>
                      </div>
                      <div className="text-right text-sm bg-blue-100 px-3 py-1 rounded-full">
                        <p className="font-medium text-blue-800">{exp.startDate} - {exp.endDate}</p>
                        {exp.location && <p className="text-blue-600">{exp.location}</p>}
                      </div>
                    </div>
                    {exp.description && exp.description.length > 0 && (
                      <ul className="space-y-2 text-gray-700">
                        {exp.description.map((desc, descIndex) => (
                          desc.trim() && (
                            <li key={descIndex} className="flex items-start">
                              <span className="text-blue-600 mr-2 mt-1">▸</span>
                              <span className="leading-relaxed">{desc}</span>
                            </li>
                          )
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {(skills.technical.length > 0 || skills.soft.length > 0 || skills.languages.length > 0) && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
              Skills & Expertise
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {skills.technical.length > 0 && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-bold text-blue-800 mb-3">Technical Skills</h3>
                  <div className="flex flex-wrap gap-2">
                    {skills.technical.map((skill, index) => (
                      <span key={index} className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {skills.soft.length > 0 && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-bold text-green-800 mb-3">Soft Skills</h3>
                  <div className="flex flex-wrap gap-2">
                    {skills.soft.map((skill, index) => (
                      <span key={index} className="bg-green-600 text-white px-3 py-1 rounded-full text-sm">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {skills.languages.length > 0 && (
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-bold text-purple-800 mb-3">Languages</h3>
                  <div className="flex flex-wrap gap-2">
                    {skills.languages.map((language, index) => (
                      <span key={index} className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
                        {language}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </section>
        )}

        {/* Education */}
        {education.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
              Education
            </h2>
            <div className="space-y-4">
              {education.map((edu, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg print-avoid-break">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{edu.degree}</h3>
                      <p className="text-blue-700 font-semibold">{edu.institution}</p>
                      {edu.field && <p className="text-gray-600">{edu.field}</p>}
                    </div>
                    <div className="text-right text-sm bg-blue-100 px-3 py-1 rounded-full">
                      <p className="font-medium text-blue-800">{edu.startDate} - {edu.endDate}</p>
                      {edu.location && <p className="text-blue-600">{edu.location}</p>}
                      {edu.gpa && <p className="text-blue-600">GPA: {edu.gpa}</p>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
              Featured Projects
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {projects.map((project, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg print-avoid-break">
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{project.name}</h3>
                  {project.description && (
                    <p className="text-gray-700 leading-relaxed mb-3">{project.description}</p>
                  )}
                  <div className="flex justify-between items-center">
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.slice(0, 3).map((tech, techIndex) => (
                          <span key={techIndex} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                    <div className="text-sm text-blue-600">
                      {project.url && <span className="mr-2">🔗 Live</span>}
                      {project.github && <span>📁 Code</span>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Certifications */}
        {certifications.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
              Certifications
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {certifications.map((cert, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg print-avoid-break">
                  <h3 className="font-bold text-gray-900">{cert.name}</h3>
                  <p className="text-blue-700">{cert.issuer}</p>
                  <p className="text-sm text-gray-600">{cert.date}</p>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default ModernTemplate;
