/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m10.852 19.772-.383.924", key: "r7sl7d" }],
  ["path", { d: "m13.148 14.228.383-.923", key: "1d5zpm" }],
  ["path", { d: "M13.148 19.772a3 3 0 1 0-2.296-5.544l-.383-.923", key: "1ydik7" }],
  ["path", { d: "m13.53 20.696-.382-.924a3 3 0 1 1-2.296-5.544", key: "1m1vsf" }],
  ["path", { d: "m14.772 15.852.923-.383", key: "660p6e" }],
  ["path", { d: "m14.772 18.148.923.383", key: "hrcpis" }],
  [
    "path",
    {
      d: "M4.2 15.1a7 7 0 1 1 9.93-9.858A7 7 0 0 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2",
      key: "j2q98n"
    }
  ],
  ["path", { d: "m9.228 15.852-.923-.383", key: "1p9ong" }],
  ["path", { d: "m9.228 18.148-.923.383", key: "6558rz" }]
];
const CloudCog = createLucideIcon("cloud-cog", __iconNode);

export { __iconNode, CloudCog as default };
//# sourceMappingURL=cloud-cog.js.map
