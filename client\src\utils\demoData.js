export const demoResumeData = {
  title: 'Software Engineer Resume',
  template: 'modern',
  personalInfo: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    website: 'https://alexjohnson.dev',
    linkedin: 'https://linkedin.com/in/alex<PERSON>hnson',
    github: 'https://github.com/alexjohnson'
  },
  summary: 'Passionate Full Stack Developer with 3+ years of experience building scalable web applications using React, Node.js, and cloud technologies. Proven track record of delivering high-quality software solutions and collaborating effectively with cross-functional teams.',
  experience: [
    {
      company: 'TechCorp Inc.',
      position: 'Senior Software Engineer',
      location: 'San Francisco, CA',
      startDate: '01/2022',
      endDate: 'Present',
      current: true,
      description: [
        'Led development of microservices architecture serving 1M+ users, improving system performance by 40%',
        'Mentored 3 junior developers and conducted code reviews to maintain high code quality standards',
        'Implemented CI/CD pipelines using Docker and Kubernetes, reducing deployment time by 60%',
        'Collaborated with product managers and designers to deliver user-centric features'
      ]
    },
    {
      company: 'StartupXYZ',
      position: 'Full Stack Developer',
      location: 'San Francisco, CA',
      startDate: '06/2020',
      endDate: '12/2021',
      current: false,
      description: [
        'Built responsive web applications using React, Redux, and Node.js for 50K+ active users',
        'Designed and implemented RESTful APIs with Express.js and MongoDB',
        'Optimized database queries resulting in 30% faster page load times',
        'Participated in agile development process and sprint planning'
      ]
    }
  ],
  education: [
    {
      institution: 'University of California, Berkeley',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      location: 'Berkeley, CA',
      startDate: '08/2016',
      endDate: '05/2020',
      gpa: '3.8/4.0'
    }
  ],
  skills: {
    technical: [
      'JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'MongoDB', 
      'PostgreSQL', 'AWS', 'Docker', 'Kubernetes', 'Git', 'REST APIs'
    ],
    soft: [
      'Leadership', 'Problem Solving', 'Communication', 'Team Collaboration', 
      'Project Management', 'Mentoring'
    ],
    languages: ['English (Native)', 'Spanish (Conversational)']
  },
  projects: [
    {
      name: 'E-Commerce Platform',
      description: 'Full-stack e-commerce application with payment integration, inventory management, and admin dashboard. Built with React, Node.js, and Stripe API.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Redux'],
      url: 'https://ecommerce-demo.com',
      github: 'https://github.com/alexjohnson/ecommerce-platform'
    },
    {
      name: 'Task Management App',
      description: 'Collaborative task management application with real-time updates, file sharing, and team communication features.',
      technologies: ['React', 'Socket.io', 'Express', 'PostgreSQL'],
      url: 'https://taskmanager-demo.com',
      github: 'https://github.com/alexjohnson/task-manager'
    }
  ],
  certifications: [
    {
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      date: '03/2023',
      url: 'https://aws.amazon.com/certification/'
    },
    {
      name: 'React Developer Certification',
      issuer: 'Meta',
      date: '01/2022',
      url: 'https://developers.facebook.com/certification/'
    }
  ]
};

export const quickFillOptions = {
  'Software Engineer Fresher': {
    summary: 'Recent Computer Science graduate with strong foundation in software development, data structures, and algorithms. Passionate about building efficient, scalable applications and eager to contribute to innovative projects.',
    skills: {
      technical: ['JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'Git', 'HTML/CSS', 'Java'],
      soft: ['Problem Solving', 'Quick Learning', 'Team Collaboration', 'Communication'],
      languages: ['English (Native)']
    }
  },
  'Frontend Developer 2 years experience': {
    summary: 'Creative Frontend Developer with 2+ years of experience building responsive, user-friendly web applications. Expertise in React, JavaScript, and modern CSS frameworks with a keen eye for design and user experience.',
    skills: {
      technical: ['React', 'JavaScript', 'TypeScript', 'CSS3', 'SASS', 'Tailwind CSS', 'Redux', 'Webpack', 'Git'],
      soft: ['UI/UX Design', 'Attention to Detail', 'Creative Problem Solving', 'Cross-browser Compatibility'],
      languages: ['English (Native)']
    }
  },
  'Full Stack Developer 3 years experience': {
    summary: 'Versatile Full Stack Developer with 3+ years of experience building end-to-end web applications. Proficient in both frontend and backend technologies with a focus on creating scalable, maintainable solutions.',
    skills: {
      technical: ['React', 'Node.js', 'JavaScript', 'TypeScript', 'MongoDB', 'PostgreSQL', 'Express.js', 'AWS', 'Docker', 'Git'],
      soft: ['Full Stack Development', 'System Architecture', 'Database Design', 'API Development', 'DevOps'],
      languages: ['English (Native)']
    }
  },
  'Data Scientist Entry Level': {
    summary: 'Analytical Data Scientist with strong foundation in statistics, machine learning, and data visualization. Experienced in Python, R, and SQL with passion for extracting insights from complex datasets.',
    skills: {
      technical: ['Python', 'R', 'SQL', 'Pandas', 'NumPy', 'Scikit-learn', 'TensorFlow', 'Tableau', 'Jupyter', 'Git'],
      soft: ['Statistical Analysis', 'Data Visualization', 'Machine Learning', 'Research', 'Critical Thinking'],
      languages: ['English (Native)']
    }
  }
};
