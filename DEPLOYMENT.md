# ResumeMate Deployment Guide

## 🚀 Deployment Options

### Option 1: Railway (Recommended for Backend)
Railway provides excellent Node.js and MongoDB hosting with automatic deployments.

#### Backend Deployment
1. **Connect Repository**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login and deploy
   railway login
   railway init
   railway up
   ```

2. **Environment Variables**
   Set in Railway dashboard:
   ```env
   NODE_ENV=production
   PORT=5000
   MONGODB_URI=mongodb+srv://...
   JWT_SECRET=your_production_jwt_secret
   CLIENT_URL=https://your-frontend-domain.com
   ```

3. **Database Setup**
   - Use Railway's MongoDB plugin, or
   - Connect to MongoDB Atlas

#### Frontend Deployment
1. **Build for Production**
   ```bash
   cd client
   npm run build
   ```

2. **Deploy to Vercel**
   ```bash
   npm install -g vercel
   vercel --prod
   ```

### Option 2: Heroku
Classic platform with good documentation and free tier.

#### Backend on Heroku
1. **Prepare for Deployment**
   ```bash
   # Create Procfile
   echo "web: node server/app.js" > Procfile
   
   # Update package.json scripts
   "scripts": {
     "start": "node server/app.js",
     "heroku-postbuild": "cd client && npm install && npm run build"
   }
   ```

2. **Deploy**
   ```bash
   heroku create resumemate-api
   heroku config:set NODE_ENV=production
   heroku config:set JWT_SECRET=your_secret
   heroku addons:create mongolab:sandbox
   git push heroku main
   ```

### Option 3: DigitalOcean App Platform
Full-stack deployment with automatic scaling.

1. **Create App Spec**
   ```yaml
   name: resumemate
   services:
   - name: api
     source_dir: /server
     github:
       repo: your-username/resumemate
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
   - name: web
     source_dir: /client
     github:
       repo: your-username/resumemate
       branch: main
     build_command: npm run build
     run_command: npm run preview
   ```

## 🔧 Production Configuration

### Environment Variables

#### Backend (.env)
```env
# Server
NODE_ENV=production
PORT=5000
CLIENT_URL=https://your-domain.com

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/resumemate

# Security
JWT_SECRET=your_super_secure_jwt_secret_for_production
SESSION_SECRET=your_session_secret

# OAuth (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# AI Features (Optional)
OPENROUTER_API_KEY=your_openrouter_api_key

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
ADMIN_EMAIL=<EMAIL>
```

#### Frontend (.env)
```env
VITE_API_URL=https://your-api-domain.com
```

### Database Setup (MongoDB Atlas)

1. **Create Cluster**
   - Sign up at [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a free cluster
   - Configure network access (0.0.0.0/0 for production)

2. **Create Database User**
   - Add database user with read/write permissions
   - Note username and password

3. **Get Connection String**
   - Click "Connect" → "Connect your application"
   - Copy connection string
   - Replace `<password>` with actual password

### Security Checklist

#### Backend Security
- [ ] Strong JWT secret (32+ characters)
- [ ] HTTPS enabled
- [ ] CORS configured for production domain
- [ ] Rate limiting enabled
- [ ] Input validation on all endpoints
- [ ] Error messages don't expose sensitive info
- [ ] Database connection secured

#### Frontend Security
- [ ] API URL points to HTTPS backend
- [ ] No sensitive data in client code
- [ ] Content Security Policy configured
- [ ] HTTPS redirect enabled

## 📊 Monitoring & Maintenance

### Health Monitoring
```javascript
// Add to server/app.js
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV
  });
});
```

### Logging
```javascript
// Production logging
if (process.env.NODE_ENV === 'production') {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}
```

### Error Tracking
Consider integrating:
- **Sentry** for error tracking
- **LogRocket** for session replay
- **New Relic** for performance monitoring

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        npm install
        cd server && npm install
        cd ../client && npm install
        
    - name: Run tests
      run: npm test
      
    - name: Build client
      run: cd client && npm run build
      
    - name: Deploy to Railway
      run: railway up --service backend
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

## 🧪 Testing Production

### Smoke Tests
```bash
# Test API health
curl https://your-api-domain.com/api/health

# Test authentication
curl -X POST https://your-api-domain.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","password":"password123"}'
```

### Load Testing
```bash
# Install artillery
npm install -g artillery

# Create test config
echo "config:
  target: 'https://your-api-domain.com'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: 'Health check'
    requests:
      - get:
          url: '/api/health'" > load-test.yml

# Run test
artillery run load-test.yml
```

## 📈 Scaling Considerations

### Database Optimization
- Index frequently queried fields
- Implement database connection pooling
- Consider read replicas for high traffic

### Caching Strategy
- Redis for session storage
- CDN for static assets
- API response caching

### Performance Monitoring
- Monitor response times
- Track error rates
- Set up alerts for downtime

## 🔒 Backup Strategy

### Database Backups
```bash
# MongoDB Atlas automatic backups
# Or manual backup
mongodump --uri="mongodb+srv://..." --out=backup/
```

### Code Backups
- Git repository with multiple remotes
- Regular tagged releases
- Environment configuration backups

---

**Note**: Always test deployments in a staging environment before production!
