const mongoose = require("mongoose");

const experienceSchema = new mongoose.Schema({
  company: {
    type: String,
    required: true,
    trim: true,
  },
  position: {
    type: String,
    required: true,
    trim: true,
  },
  location: {
    type: String,
    trim: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    default: "Present",
  },
  current: {
    type: Boolean,
    default: false,
  },
  description: [
    {
      type: String,
      trim: true,
    },
  ],
});

const educationSchema = new mongoose.Schema({
  institution: {
    type: String,
    required: true,
    trim: true,
  },
  degree: {
    type: String,
    required: true,
    trim: true,
  },
  field: {
    type: String,
    trim: true,
  },
  location: {
    type: String,
    trim: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  },
  gpa: {
    type: String,
    trim: true,
  },
  honors: [
    {
      type: String,
      trim: true,
    },
  ],
});

const projectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  technologies: [
    {
      type: String,
      trim: true,
    },
  ],
  url: {
    type: String,
    trim: true,
  },
  github: {
    type: String,
    trim: true,
  },
  startDate: String,
  endDate: String,
});

const resumeSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
      default: "My Resume",
    },
    template: {
      type: String,
      enum: ["minimalist", "modern", "creative"],
      default: "minimalist",
    },
    personalInfo: {
      fullName: {
        type: String,
        required: true,
        trim: true,
      },
      email: {
        type: String,
        required: true,
        trim: true,
      },
      phone: {
        type: String,
        trim: true,
      },
      location: {
        type: String,
        trim: true,
      },
      website: {
        type: String,
        trim: true,
      },
      linkedin: {
        type: String,
        trim: true,
      },
      github: {
        type: String,
        trim: true,
      },
    },
    summary: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    experience: [experienceSchema],
    education: [educationSchema],
    skills: {
      technical: [
        {
          type: String,
          trim: true,
        },
      ],
      soft: [
        {
          type: String,
          trim: true,
        },
      ],
      languages: [
        {
          type: String,
          trim: true,
        },
      ],
    },
    projects: [projectSchema],
    certifications: [
      {
        name: {
          type: String,
          required: true,
          trim: true,
        },
        issuer: {
          type: String,
          required: true,
          trim: true,
        },
        date: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          trim: true,
        },
      },
    ],
    isPublic: {
      type: Boolean,
      default: false,
    },
    shareableLink: {
      type: String,
      unique: true,
      sparse: true,
    },
  },
  {
    timestamps: true,
  }
);

// Index for better query performance
resumeSchema.index({ user: 1 });
resumeSchema.index({ createdAt: -1 });
// shareableLink index is already defined in schema field with unique: true and sparse: true

// Generate shareable link before saving
resumeSchema.pre("save", function (next) {
  if (this.isPublic && !this.shareableLink) {
    this.shareableLink = require("crypto").randomBytes(16).toString("hex");
  }
  next();
});

module.exports = mongoose.model("Resume", resumeSchema);
