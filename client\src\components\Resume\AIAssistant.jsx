import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Wand2, Brain, Target, Lightbulb } from 'lucide-react';
import { aiAPI } from '../../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../UI/LoadingSpinner';

const AIAssistant = ({ onClose, resumeData, onUpdateResume }) => {
  const [activeTab, setActiveTab] = useState('autofill');
  const [loading, setLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [jobDescription, setJobDescription] = useState('');

  const quickPrompts = [
    'Software Engineer Fresher',
    'Frontend Developer 2 years experience',
    'Full Stack Developer 3 years experience',
    'Data Scientist Entry Level',
    'Product Manager 5 years experience',
    'UI/UX Designer 2 years experience',
    'DevOps Engineer 4 years experience',
    'Marketing Manager 3 years experience'
  ];

  const handleAutofill = async (selectedPrompt) => {
    setLoading(true);
    try {
      // Generate summary
      const summaryResponse = await aiAPI.generateSummary({
        jobTitle: selectedPrompt,
        experience: resumeData.experience?.length || 0,
        skills: resumeData.skills?.technical || []
      });

      // Suggest skills
      const skillsResponse = await aiAPI.suggestSkills({
        jobTitle: selectedPrompt
      });

      // Update resume data
      onUpdateResume(prev => ({
        ...prev,
        summary: summaryResponse.data.summary || prev.summary,
        skills: {
          ...prev.skills,
          technical: [...new Set([...prev.skills.technical, ...skillsResponse.data.skills.slice(0, 8)])]
        }
      }));

      toast.success('AI autofill completed!');
      onClose();
    } catch (error) {
      toast.error('Failed to generate AI content');
    } finally {
      setLoading(false);
    }
  };

  const handleJobAnalysis = async () => {
    if (!jobDescription.trim()) {
      toast.error('Please enter a job description');
      return;
    }

    setLoading(true);
    try {
      const response = await aiAPI.analyzeJobDescription({
        jobDescription: jobDescription.trim()
      });

      const { keySkills, requirements, suggestions } = response.data;

      // Show analysis results
      toast.success('Job description analyzed!');
      
      // Update skills based on analysis
      if (keySkills && keySkills.length > 0) {
        onUpdateResume(prev => ({
          ...prev,
          skills: {
            ...prev.skills,
            technical: [...new Set([...prev.skills.technical, ...keySkills.slice(0, 6)])]
          }
        }));
      }

    } catch (error) {
      toast.error('Failed to analyze job description');
    } finally {
      setLoading(false);
    }
  };

  const handleImproveExperience = async (expIndex) => {
    const experience = resumeData.experience[expIndex];
    if (!experience) return;

    setLoading(true);
    try {
      const response = await aiAPI.improveExperience({
        position: experience.position,
        company: experience.company,
        description: experience.description.join('. ')
      });

      // Update the experience with improved description
      onUpdateResume(prev => ({
        ...prev,
        experience: prev.experience.map((exp, index) => 
          index === expIndex 
            ? { ...exp, description: response.data.improvedDescription }
            : exp
        )
      }));

      toast.success('Experience improved with AI!');
    } catch (error) {
      toast.error('Failed to improve experience');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'autofill', label: 'Quick Autofill', icon: Sparkles },
    { id: 'job-analysis', label: 'Job Analysis', icon: Target },
    { id: 'improve', label: 'Improve Content', icon: Wand2 }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="h-8 w-8" />
              <div>
                <h2 className="text-2xl font-bold">AI Resume Assistant</h2>
                <p className="text-purple-100">Enhance your resume with AI-powered suggestions</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Quick Autofill */}
          {activeTab === 'autofill' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Quick Resume Autofill
                </h3>
                <p className="text-gray-600 mb-4">
                  Select your role and experience level to automatically generate a professional summary and relevant skills.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Prompt
                </label>
                <input
                  type="text"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="input mb-4"
                  placeholder="e.g., Senior React Developer with 5 years experience"
                />
                <button
                  onClick={() => handleAutofill(prompt)}
                  disabled={!prompt.trim() || loading}
                  className="btn-primary mr-4"
                >
                  {loading ? <LoadingSpinner size="sm" /> : 'Generate Content'}
                </button>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Quick Options</h4>
                <div className="grid grid-cols-2 gap-3">
                  {quickPrompts.map((quickPrompt, index) => (
                    <button
                      key={index}
                      onClick={() => handleAutofill(quickPrompt)}
                      disabled={loading}
                      className="btn-outline text-left p-3 hover:bg-purple-50 hover:border-purple-300"
                    >
                      <Lightbulb className="h-4 w-4 inline mr-2 text-purple-500" />
                      {quickPrompt}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Job Analysis */}
          {activeTab === 'job-analysis' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Job Description Analysis
                </h3>
                <p className="text-gray-600 mb-4">
                  Paste a job description to get AI-powered insights and optimize your resume for that specific role.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Job Description
                </label>
                <textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  className="textarea"
                  rows={8}
                  placeholder="Paste the job description here..."
                />
              </div>

              <button
                onClick={handleJobAnalysis}
                disabled={!jobDescription.trim() || loading}
                className="btn-primary"
              >
                {loading ? <LoadingSpinner size="sm" /> : 'Analyze Job Description'}
              </button>
            </div>
          )}

          {/* Improve Content */}
          {activeTab === 'improve' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Improve Existing Content
                </h3>
                <p className="text-gray-600 mb-4">
                  Use AI to enhance your existing work experience descriptions with more impactful language and better formatting.
                </p>
              </div>

              {resumeData.experience && resumeData.experience.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Work Experience</h4>
                  {resumeData.experience.map((exp, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-medium text-gray-900">{exp.position}</h5>
                          <p className="text-gray-600">{exp.company}</p>
                        </div>
                        <button
                          onClick={() => handleImproveExperience(index)}
                          disabled={loading}
                          className="btn-primary btn-sm"
                        >
                          {loading ? <LoadingSpinner size="sm" /> : 'Improve with AI'}
                        </button>
                      </div>
                      {exp.description && exp.description.length > 0 && (
                        <ul className="text-sm text-gray-600 list-disc list-inside">
                          {exp.description.slice(0, 2).map((desc, descIndex) => (
                            <li key={descIndex}>{desc}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Wand2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>Add some work experience first to use this feature.</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              💡 Tip: AI suggestions are starting points. Always review and customize the content to match your experience.
            </p>
            <button
              onClick={onClose}
              className="btn-outline"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
