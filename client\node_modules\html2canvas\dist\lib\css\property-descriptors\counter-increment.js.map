{"version": 3, "file": "counter-increment.js", "sourceRoot": "", "sources": ["../../../../src/css/property-descriptors/counter-increment.ts"], "names": [], "mappings": ";;;AACA,2CAAwE;AAW3D,QAAA,gBAAgB,GAA8C;IACvE,IAAI,EAAE,mBAAmB;IACzB,YAAY,EAAE,MAAM;IACpB,MAAM,EAAE,IAAI;IACZ,IAAI,cAAoC;IACxC,KAAK,EAAE,UAAC,QAAiB,EAAE,MAAkB;QACzC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAExB,IAAI,KAAK,CAAC,IAAI,yBAA0B,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;YAChE,OAAO,IAAI,CAAC;SACf;QAED,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAa,CAAC,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,OAAO,CAAC,IAAI,yBAA0B,EAAE;gBACxC,IAAM,SAAS,GAAG,IAAI,IAAI,sBAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE,UAAU,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,SAAS,WAAA,EAAC,CAAC,CAAC;aACxD;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ,CAAC"}