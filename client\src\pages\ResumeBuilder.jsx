import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useResume } from "../context/ResumeContext";
import { aiAPI } from "../services/api";
import toast from "react-hot-toast";
import {
  Save,
  Download,
  Share2,
  Wand2,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  Sparkles,
  FileText,
} from "lucide-react";
import ResumeForm from "../components/Resume/ResumeForm";
import ResumePreview from "../components/Resume/ResumePreview";
import TemplateSelector from "../components/Resume/TemplateSelector";
import AIAssistant from "../components/Resume/AIAssistant";
import LoadingSpinner from "../components/UI/LoadingSpinner";
import { demoResumeData } from "../utils/demoData";

const ResumeBuilder = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    currentResume,
    fetchResume,
    createResume,
    updateResume,
    autoSaveResume,
    loading,
  } = useResume();

  const [resumeData, setResumeData] = useState({
    title: "My Resume",
    template: "minimalist",
    personalInfo: {
      fullName: "",
      email: "",
      phone: "",
      location: "",
      website: "",
      linkedin: "",
      github: "",
    },
    summary: "",
    experience: [],
    education: [],
    skills: {
      technical: [],
      soft: [],
      languages: [],
    },
    projects: [],
    certifications: [],
  });

  const [showPreview, setShowPreview] = useState(true);
  const [showAI, setShowAI] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  // Load resume data
  useEffect(() => {
    if (id) {
      fetchResume(id);
    }
  }, [id, fetchResume]);

  useEffect(() => {
    if (currentResume) {
      setResumeData(currentResume);
    }
  }, [currentResume]);

  // Auto-save functionality
  useEffect(() => {
    if (id && resumeData.title && resumeData.personalInfo.fullName) {
      const timeoutId = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [resumeData, id]); // Keep dependencies but add condition to prevent empty saves

  const handleAutoSave = async () => {
    if (id && resumeData.title) {
      setSaving(true);
      const result = await autoSaveResume(id, resumeData);
      if (result) {
        setLastSaved(new Date());
      }
      setSaving(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      if (id) {
        await updateResume(id, resumeData);
        toast.success("Resume saved successfully!");
      } else {
        const newResume = await createResume(resumeData);
        if (newResume) {
          navigate(`/resume-builder/${newResume._id}`, { replace: true });
          toast.success("Resume created successfully!");
        }
      }
      setLastSaved(new Date());
    } catch (error) {
      toast.error("Failed to save resume");
    } finally {
      setSaving(false);
    }
  };

  const handleExportPDF = () => {
    const element = document.getElementById("resume-preview");
    if (element) {
      import("html2pdf.js").then((html2pdf) => {
        const opt = {
          margin: 0.5,
          filename: `${resumeData.personalInfo.fullName || "resume"}.pdf`,
          image: { type: "jpeg", quality: 0.98 },
          html2canvas: { scale: 2 },
          jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
        };
        html2pdf.default().set(opt).from(element).save();
      });
    }
  };

  const handleAIAutofill = async (prompt) => {
    try {
      const response = await aiAPI.generateSummary({
        jobTitle: prompt,
        experience: resumeData.experience.length,
        skills: resumeData.skills.technical,
      });

      if (response.data.summary) {
        setResumeData((prev) => ({
          ...prev,
          summary: response.data.summary,
        }));
        toast.success("AI summary generated!");
      }
    } catch (error) {
      toast.error("Failed to generate AI content");
    }
  };

  const loadDemoData = () => {
    setResumeData(demoResumeData);
    toast.success("Demo data loaded!");
  };

  if (loading && !resumeData.title) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                {resumeData.title}
              </h1>
              {saving && (
                <div className="flex items-center text-sm text-gray-500">
                  <LoadingSpinner size="sm" className="mr-2" />
                  Saving...
                </div>
              )}
              {lastSaved && !saving && (
                <span className="text-sm text-gray-500">
                  Saved {lastSaved.toLocaleTimeString()}
                </span>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <button onClick={loadDemoData} className="btn-outline btn-sm">
                <FileText className="h-4 w-4 mr-2" />
                Load Demo
              </button>

              <button
                onClick={() => setShowAI(!showAI)}
                className="btn-outline btn-sm"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                AI Assistant
              </button>

              <button
                onClick={() => setShowPreview(!showPreview)}
                className="btn-outline btn-sm md:hidden"
              >
                {showPreview ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>

              <button
                onClick={handleSave}
                disabled={saving}
                className="btn-primary btn-sm"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </button>

              <button
                onClick={handleExportPDF}
                className="btn-secondary btn-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div className={`${showPreview ? "hidden lg:block" : "block"}`}>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6">
                <TemplateSelector
                  selectedTemplate={resumeData.template}
                  onTemplateChange={(template) =>
                    setResumeData((prev) => ({ ...prev, template }))
                  }
                />

                <ResumeForm resumeData={resumeData} onChange={setResumeData} />
              </div>
            </div>
          </div>

          {/* Preview Section */}
          <div className={`${showPreview ? "block" : "hidden lg:block"}`}>
            <div className="sticky top-24">
              <ResumePreview
                resumeData={resumeData}
                template={resumeData.template}
              />
            </div>
          </div>
        </div>
      </div>

      {/* AI Assistant Modal */}
      {showAI && (
        <AIAssistant
          onClose={() => setShowAI(false)}
          onApply={handleAIAutofill}
          resumeData={resumeData}
          onUpdateResume={setResumeData}
        />
      )}
    </div>
  );
};

export default ResumeBuilder;
