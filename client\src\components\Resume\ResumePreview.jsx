import React from 'react';
import MinimalistTemplate from '../Templates/MinimalistTemplate';
import ModernTemplate from '../Templates/ModernTemplate';
import CreativeTemplate from '../Templates/CreativeTemplate';

const ResumePreview = ({ resumeData, template }) => {
  const renderTemplate = () => {
    switch (template) {
      case 'modern':
        return <ModernTemplate data={resumeData} />;
      case 'creative':
        return <CreativeTemplate data={resumeData} />;
      case 'minimalist':
      default:
        return <MinimalistTemplate data={resumeData} />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900">Preview</h3>
      </div>
      
      <div className="p-4">
        <div 
          id="resume-preview"
          className="bg-white shadow-lg mx-auto"
          style={{ 
            width: '210mm', 
            minHeight: '297mm',
            maxWidth: '100%',
            transform: 'scale(0.6)',
            transformOrigin: 'top center',
            marginBottom: '-40%'
          }}
        >
          {renderTemplate()}
        </div>
      </div>
    </div>
  );
};

export default ResumePreview;
