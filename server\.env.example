# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:5173

# Database
MONGODB_URI=mongodb+srv://Chat-App:<EMAIL>/chatapp?retryWrites=true&w=majority&appName=Cluster0

# JWT Secret
JWT_SECRET=99f76802366732b6dc25bb7acf7475f6881b821a49d7a4dd668073e239d639bbe0ef716629df6d8ddf69c2219f9b901203c7fd71373c92065bef8c9a954627bf

# Google OAuth
GOOGLE_CLIENT_ID=285275175306-us88v5jjobl0on73kita5a0lju77ke8r.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# OpenRouter API (for AI features)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Email Configuration (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=zlzp giun hqxn uvwo
ADMIN_EMAIL=<EMAIL>

# Session Secret
SESSION_SECRET=32d67c3de25a4e8586c59e2732d1745c7a2e7d42c36c418cbc0b56a2a4ad52a8